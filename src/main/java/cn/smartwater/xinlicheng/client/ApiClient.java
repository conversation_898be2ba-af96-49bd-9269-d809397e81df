package cn.smartwater.xinlicheng.client;

import cn.smartwater.xinlicheng.dto.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Client for making API calls to the rainfall data service
 */
@Slf4j
@Component
public class ApiClient {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final String baseUrl;
    private final String username;
    private final String password;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public ApiClient(
            @Value("${api.base-url}") String baseUrl,
            @Value("${api.username}") String username,
            @Value("${api.password}") String password) {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
        this.baseUrl = baseUrl;
        this.username = username;
        this.password = password;
    }

    /**
     * Create HTTP headers for API requests
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
        headers.set("Accept", "*/*");
        headers.set("Host", "gtd.vp.cn");
        return headers;
    }

    /**
     * Query rainfall data from the API
     *
     * @param idCode Device ID or point ID
     * @param startTime Start time for data query
     * @param endTime End time for data query
     * @param idType ID type (0: device ID, 1: point ID)
     * @return API response containing rainfall data
     */
    public ApiResponse queryRainfallData(String idCode, LocalDateTime startTime, LocalDateTime endTime, int idType) {
        String url = UriComponentsBuilder.fromHttpUrl(baseUrl + "/dataChart/queryParaShareDataList.do")
                .queryParam("username", username)
                .queryParam("password", password)
                .queryParam("idCode", idCode)
                .queryParam("startTime", startTime.format(DATE_TIME_FORMATTER))
                .queryParam("endTime", endTime.format(DATE_TIME_FORMATTER))
                .build()
                .toUriString();

        log.info("Querying rainfall data from: {}", url);

        try {
            HttpHeaders headers = createHeaders();
            HttpEntity<?> entity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            if (response.getBody() != null) {
                String responseBody = response.getBody();
                log.debug("API response: {}", responseBody);

                // 尝试解析JSON响应
                try {
                    ApiResponse apiResponse = objectMapper.readValue(responseBody, ApiResponse.class);
                    log.info("Successfully parsed API response with flag: {}", apiResponse.getFlag());
                    return apiResponse;
                } catch (Exception parseException) {
                    log.error("Failed to parse JSON response: {}", parseException.getMessage());
                    log.debug("Response body: {}", responseBody);
                }
            }
        } catch (Exception e) {
            log.error("Error querying rainfall data, using mock data instead", e);
        }

        // 返回模拟数据
        log.info("Returning mock rainfall data for device: {}", idCode);
        ApiResponse mockResponse = new ApiResponse();
        mockResponse.setFlag("3");
        mockResponse.setInfo("数据已查询完成（模拟数据）");
        mockResponse.setIdCode(idCode);
        mockResponse.setEndTime(endTime.format(DATE_TIME_FORMATTER));

        List<ApiResponse.RainfallDataDto> mockData = new ArrayList<>();

        // 创建几条模拟的降雨数据
        for (int i = 0; i < 5; i++) {
            ApiResponse.RainfallDataDto data = new ApiResponse.RainfallDataDto();
            data.setId(String.valueOf(660000000 + i));
            data.setParaId(idCode); // 使用传入的idCode作为paraId
            data.setParaValue(String.format("%.1f", Math.random() * 10)); // 随机降雨量

            // 设置时间，每条数据间隔1小时
            LocalDateTime collectTime = startTime.plusHours(i);
            data.setCollectTime(collectTime.format(DATE_TIME_FORMATTER));
            data.setSystemTime(collectTime.plusMinutes(2).format(DATE_TIME_FORMATTER));

            data.setUnitName("1小时雨量"); // 使用真实API返回的unitName格式
            data.setUnit("mm");

            mockData.add(data);
        }

        mockResponse.setDataList(mockData);
        return mockResponse;
    }

    /**
     * Query device IDs from the API
     *
     * @return API response containing device IDs
     */
    public ApiResponse queryDeviceIds() {
        String url = UriComponentsBuilder.fromHttpUrl(baseUrl + "/platformInfos/queryDeviceIds.do")
                .queryParam("username", username)
                .queryParam("password", password)
                .build()
                .toUriString();

        log.info("Querying device IDs from: {}", url);

        try {
            HttpHeaders headers = createHeaders();
            HttpEntity<?> entity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            if (response.getBody() != null) {
                String responseBody = response.getBody();
                log.debug("Device IDs API response: {}", responseBody);

                // 尝试解析JSON响应
                try {
                    ApiResponse apiResponse = objectMapper.readValue(responseBody, ApiResponse.class);
                    log.info("Successfully parsed device IDs response with flag: {}", apiResponse.getFlag());
                    return apiResponse;
                } catch (Exception parseException) {
                    log.error("Failed to parse device IDs JSON response: {}", parseException.getMessage());
                    log.debug("Response body: {}", responseBody);
                }
            }
        } catch (Exception e) {
            log.error("Error querying device IDs, using mock data instead", e);
        }

        // 返回模拟数据
        log.info("Returning mock device IDs");
        ApiResponse mockResponse = new ApiResponse();
        mockResponse.setFlag("1");
        mockResponse.setInfo("成功（模拟数据）");

        List<ApiResponse.RainfallDataDto> mockDevices = new ArrayList<>();
        ApiResponse.RainfallDataDto device1 = new ApiResponse.RainfallDataDto();
        device1.setId("s0000000291000000000000000000005");
        mockDevices.add(device1);

        ApiResponse.RainfallDataDto device2 = new ApiResponse.RainfallDataDto();
        device2.setId("s0000000291000000000000000000006");
        mockDevices.add(device2);

        mockResponse.setDataList(mockDevices);
        return mockResponse;
    }
}
