package cn.smartwater.xinlicheng.service;

import java.time.LocalDateTime;

/**
 * Service interface for rainfall data operations
 */
public interface RainfallService {

    /**
     * Fetch rainfall data from the API and save to rtdb.dbo.st_pptn_r table
     *
     * @param startTime Start time for data query
     * @param endTime End time for data query
     * @return Number of records saved
     */
    int fetchAndSaveRainfallData(LocalDateTime startTime, LocalDateTime endTime);
}
