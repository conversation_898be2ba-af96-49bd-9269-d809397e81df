package cn.smartwater.xinlicheng.service.impl;

import cn.smartwater.xinlicheng.client.ApiClient;
import cn.smartwater.xinlicheng.dto.ApiResponse;
import cn.smartwater.xinlicheng.entity.StPptnR;
import cn.smartwater.xinlicheng.mapper.StPptnRMapper;
import cn.smartwater.xinlicheng.service.RainfallService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Implementation of RainfallService
 */
@Slf4j
@Service
public class RainfallServiceImpl implements RainfallService {

    private final ApiClient apiClient;
    private final StPptnRMapper stPptnRMapper;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public RainfallServiceImpl(ApiClient apiClient, StPptnRMapper stPptnRMapper) {
        this.apiClient = apiClient;
        this.stPptnRMapper = stPptnRMapper;
    }

    @Override
    @Transactional
    public int fetchAndSaveRainfallData(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("Fetching rainfall data from {} to {}", startTime, endTime);

        // Get device IDs
        ApiResponse deviceResponse = apiClient.queryDeviceIds();
        if (deviceResponse == null || !"1".equals(deviceResponse.getFlag()) || CollectionUtils.isEmpty(deviceResponse.getDataList())) {
            log.warn("Failed to get device IDs or no devices found");
            return 0;
        }

        List<String> deviceIds = deviceResponse.getDataList().stream()
                .map(data -> (String) ((ApiResponse.RainfallDataDto) data).getId())
                .toList();

        log.info("Found {} device IDs: {}", deviceIds.size(), deviceIds);

        int totalFound = 0;

        // For each device, query rainfall data
        for (String deviceId : deviceIds) {
            ApiResponse response = apiClient.queryRainfallData(deviceId, startTime, endTime, 0);

            if (response == null || CollectionUtils.isEmpty(response.getDataList())) {
                log.warn("No rainfall data found for device {}", deviceId);
                continue;
            }

            List<StPptnR> rainfallDataList = new ArrayList<>();

            // Convert API response to entity objects
            for (ApiResponse.RainfallDataDto dto : response.getDataList()) {
                // Check if this is rainfall data by looking at the unit name or unit
                if (dto.getUnitName() != null && (
                        dto.getUnitName().contains("雨量") ||
                        dto.getUnitName().contains("降雨") ||
                        dto.getUnitName().contains("1小时雨量") ||
                        "mm".equals(dto.getUnit()))) {

                    StPptnR stPptnR = new StPptnR();
                    // 根据实际表结构调整字段映射
                    stPptnR.setOriginalId(dto.getId());
                    stPptnR.setParaId(dto.getParaId());
                    stPptnR.setStationId(deviceId); // 使用设备ID作为站点ID
                    stPptnR.setRainfallValue(Double.parseDouble(dto.getParaValue()));
                    stPptnR.setCollectTime(LocalDateTime.parse(dto.getCollectTime(), DATE_TIME_FORMATTER));
                    stPptnR.setSystemTime(LocalDateTime.parse(dto.getSystemTime(), DATE_TIME_FORMATTER));
                    stPptnR.setUnitName(dto.getUnitName());
                    stPptnR.setUnit(dto.getUnit());
                    stPptnR.setDataStatus("1"); // 假设1表示正常数据
                    stPptnR.setCreatedTime(LocalDateTime.now());
                    stPptnR.setUpdatedTime(LocalDateTime.now());

                    rainfallDataList.add(stPptnR);

                    // 详细记录每条降雨数据
                    log.debug("Found rainfall data: ID={}, ParaID={}, Value={}, CollectTime={}, Unit={}",
                            dto.getId(), dto.getParaId(), dto.getParaValue(), dto.getCollectTime(), dto.getUnit());
                }
            }

            // 保存到ST_PPTN_R表
            if (!rainfallDataList.isEmpty()) {
                for (StPptnR data : rainfallDataList) {
                    stPptnRMapper.insert(data);
                }
                totalFound += rainfallDataList.size();
                log.info("Saved {} rainfall data records to ST_PPTN_R for device {}", rainfallDataList.size(), deviceId);

                // 记录第一条数据的详细信息作为示例
                if (!rainfallDataList.isEmpty()) {
                    StPptnR sample = rainfallDataList.get(0);
                    log.info("Sample rainfall data: ID={}, ParaID={}, Value={}, CollectTime={}, Unit={}",
                            sample.getOriginalId(), sample.getParaId(), sample.getRainfallValue(),
                            sample.getCollectTime(), sample.getUnit());
                }
            }
        }

        log.info("Total rainfall data records saved: {}", totalFound);
        return totalFound;
    }


}
