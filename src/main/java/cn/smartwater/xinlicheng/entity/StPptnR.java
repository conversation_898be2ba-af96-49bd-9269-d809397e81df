package cn.smartwater.xinlicheng.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Entity class for ST_PPTN_R table (降雨数据表)
 * 请根据实际的表结构调整字段名称和类型
 */
@Data
@TableName("rtdb.dbo.st_pptn_r")
public class StPptnR {

    /**
     * 主键ID - 请根据实际表结构调整
     */
    @TableId
    private String id;

    /**
     * 站点ID/测点ID - 请根据实际字段名调整
     */
    private String stationId;

    /**
     * 参数ID - 对应API返回的paraId
     */
    private String paraId;

    /**
     * 降雨量值 - 对应API返回的paraValue
     */
    private Double rainfallValue;

    /**
     * 采集时间 - 对应API返回的collectTime
     */
    private LocalDateTime collectTime;

    /**
     * 系统时间 - 对应API返回的systemTime
     */
    private LocalDateTime systemTime;

    /**
     * 物理量名称 - 对应API返回的unitName
     */
    private String unitName;

    /**
     * 单位 - 对应API返回的unit
     */
    private String unit;

    /**
     * 数据状态 - 可能的字段，请根据实际情况调整
     */
    private String dataStatus;

    /**
     * 创建时间 - 记录插入时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间 - 记录更新时间
     */
    private LocalDateTime updatedTime;

    // 以下是一些可能的字段，请根据实际表结构进行调整或删除

    /**
     * 原始数据ID - 对应API返回的id
     */
    private String originalId;

    /**
     * 数据质量标识
     */
    private String qualityFlag;

    /**
     * 备注信息
     */
    private String remarks;
}
