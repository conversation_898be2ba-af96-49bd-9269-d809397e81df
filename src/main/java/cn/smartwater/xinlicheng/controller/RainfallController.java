package cn.smartwater.xinlicheng.controller;

import cn.smartwater.xinlicheng.service.RainfallService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * REST controller for rainfall data - 只提供API数据获取功能
 */
@Slf4j
@RestController
@RequestMapping("/api/rainfall")
public class RainfallController {

    private final RainfallService rainfallService;

    public RainfallController(RainfallService rainfallService) {
        this.rainfallService = rainfallService;
    }

    /**
     * Manually trigger data update
     *
     * @param hours Number of hours to look back (default: 24)
     * @return Result of the update operation
     */
    @PostMapping("/update")
    public ResponseEntity<Map<String, Object>> updateRainfallData(
            @RequestParam(defaultValue = "24") int hours) {

        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusHours(hours);

        log.info("Manually updating rainfall data for the last {} hours", hours);
        int recordsSaved = rainfallService.fetchAndSaveRainfallData(startTime, endTime);

        return ResponseEntity.ok(Map.of(
                "success", true,
                "recordsSaved", recordsSaved,
                "startTime", startTime,
                "endTime", endTime
        ));
    }
}
