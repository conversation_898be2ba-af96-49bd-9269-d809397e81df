-- 使用rtdb数据库
USE rtdb;
GO

-- 检查st_pptn_r表是否存在，如果不存在则创建
-- 注意：请根据实际的表结构调整以下字段定义
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[st_pptn_r]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[st_pptn_r] (
        [id] [varchar](50) NOT NULL,
        [station_id] [varchar](50) NULL,
        [para_id] [varchar](100) NOT NULL,
        [rainfall_value] [float] NOT NULL,
        [collect_time] [datetime2](7) NOT NULL,
        [system_time] [datetime2](7) NOT NULL,
        [unit_name] [nvarchar](100) NULL,
        [unit] [varchar](20) NULL,
        [data_status] [varchar](10) NULL,
        [original_id] [varchar](50) NULL,
        [quality_flag] [varchar](10) NULL,
        [remarks] [nvarchar](500) NULL,
        [created_time] [datetime2](7) NOT NULL,
        [updated_time] [datetime2](7) NOT NULL,
        CONSTRAINT [PK_st_pptn_r] PRIMARY KEY CLUSTERED ([id] ASC)
    );

    -- Create index on collect_time for faster queries
    CREATE INDEX [IX_st_pptn_r_collect_time] ON [dbo].[st_pptn_r] ([collect_time]);

    -- Create index on para_id for faster queries
    CREATE INDEX [IX_st_pptn_r_para_id] ON [dbo].[st_pptn_r] ([para_id]);

    -- Create index on station_id for faster queries
    CREATE INDEX [IX_st_pptn_r_station_id] ON [dbo].[st_pptn_r] ([station_id]);
END
GO

-- 如果表已存在，可以在这里添加缺失的列（如果需要的话）
-- 例如：
-- IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('st_pptn_r') AND name = 'created_time')
-- BEGIN
--     ALTER TABLE st_pptn_r ADD created_time datetime2(7) NULL;
-- END
