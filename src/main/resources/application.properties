spring.application.name=xinlicheng

# Database Configuration for rtdb database
spring.datasource.url=******************************************************************************************
spring.datasource.username=sa
spring.datasource.password=YourPassword
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver

# MyBatis Plus Configuration
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=cn.smartwater.xinlicheng.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.global-config.db-config.id-type=assign_id

# Logging Configuration
logging.level.cn.smartwater.xinlicheng=DEBUG

# API Configuration
api.base-url=http://gtd.vp.cn
api.username=xlcsk
api.password=666666

# Scheduling Configuration (update every hour)
rainfall.scheduler.cron=0 0 * * * *
